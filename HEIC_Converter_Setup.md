# 🔄 HEIC转PNG转换器

## 🎯 功能说明

纯粹的HEIC转PNG转换工具：
- **HEIC/HEIF文件** → 转换为PNG格式，保存在同一文件夹
- **其他格式** → 跳过处理
- **自动修正图片方向**（解决旋转问题）

## 🔧 Keyboard Maestro 配置

### 步骤1：For Each Path in Finder Selection
- **Action**: For Each Path in Finder Selection
- **Variable**: `ImagePath`

### 步骤2：Execute Shell Script
- **Action**: Execute Shell Script
- **Shell**: `/Library/Frameworks/Python.framework/Versions/3.13/bin/python3`
- **Script**:
```bash
/Library/Frameworks/Python.framework/Versions/3.13/bin/python3 /Users/<USER>/Documents/augment-projects/heic_to_png_converter.py
```

## 📝 使用方法

1. **在Finder中选中HEIC文件**（可多选）
2. **触发Keyboard Maestro宏**
3. **脚本自动转换HEIC为PNG**
4. **PNG文件保存在同一文件夹**

## ✅ 功能特点

### 智能处理
- 只处理HEIC/HEIF格式文件
- 其他格式文件自动跳过

### 文件命名
- 默认：`photo.heic` → `photo.png`
- 如果PNG已存在：`photo_20250805_143022.png`（带时间戳）

### 图片质量
- 自动修正EXIF方向（解决旋转问题）
- 保持原始图片质量
- 优化PNG文件大小

## 📋 输出示例

### HEIC文件：
```
🚀 HEIC转PNG转换器启动
📁 目标文件: IMG_1234.heic
🔍 处理文件: IMG_1234.heic (格式: .heic)
🔄 检测到HEIC格式，开始转换...
🔄 转换 IMG_1234.heic -> IMG_1234.png
📐 已自动校正图片方向
✅ 转换完成: /path/to/IMG_1234.png
🎉 处理完成
```

### 非HEIC文件：
```
🚀 HEIC转PNG转换器启动
📁 目标文件: photo.jpg
🔍 处理文件: photo.jpg (格式: .jpg)
ℹ️  非HEIC格式 (.jpg)，跳过转换
🎉 处理完成
```

## 🧪 手动测试

```bash
# 设置测试变量
export KMVAR_ImagePath="/path/to/your/photo.heic"

# 运行脚本
/Library/Frameworks/Python.framework/Versions/3.13/bin/python3 /Users/<USER>/Documents/augment-projects/heic_to_png_converter.py
```

## 🔧 与上传版本的区别

| 功能 | 转换器版本 | 上传版本 |
|------|------------|----------|
| HEIC转PNG | ✅ | ✅ |
| 修正方向 | ✅ | ✅ |
| 上传图床 | ❌ | ✅ |
| 处理JPG/PNG | 跳过 | 直接上传 |
| 用途 | 纯转换 | 转换+上传 |

现在您有两个脚本可以选择使用！
