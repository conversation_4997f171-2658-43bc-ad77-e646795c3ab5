# 📤 Keyboard Maestro + PicGo 自动上传配置

## 🎯 功能说明

这个脚本会自动处理选中的图片文件：
- **JPG/PNG** → 直接上传到图床
- **HEIC** → 先转换为PNG（修正方向），再上传到图床

## 🔧 Keyboard Maestro 配置

### 步骤1：For Each Path in Finder Selection
- **Action**: For Each Path in Finder Selection
- **Variable**: `ImagePath`

### 步骤2：Execute Shell Script
- **Action**: Execute Shell Script
- **Shell**: `/Library/Frameworks/Python.framework/Versions/3.13/bin/python3`
- **Script**:
```bash
/Library/Frameworks/Python.framework/Versions/3.13/bin/python3 /Users/<USER>/Documents/augment-projects/heic_converter_uploader.py
```

## 📝 工作流程

1. **在Finder中选中图片文件**（可多选）
2. **触发Keyboard Maestro宏**
3. **脚本自动判断文件格式**：
   - JPG/PNG：直接上传
   - HEIC：转换为PNG后上传
4. **上传完成**

## ✅ 脚本功能

### 自动格式判断
- 检查文件扩展名（.jpg, .jpeg, .png, .heic, .heif）
- 根据格式选择处理方式

### HEIC转换功能
- 自动修正图片方向（解决旋转问题）
- 转换为PNG格式
- 保存在源文件夹中

### PicGo上传
- 调用PicGo命令行上传图片
- 支持您现有的PicGo配置

## 🧪 测试方法

### 测试1：PNG/JPG文件
1. 选中一个PNG或JPG文件
2. 运行宏
3. 应该直接上传

### 测试2：HEIC文件
1. 选中一个HEIC文件
2. 运行宏
3. 应该先转换为PNG，再上传

### 手动测试脚本
```bash
# 设置测试变量
export KMVAR_ImagePath="/path/to/your/image.heic"

# 运行脚本
/Library/Frameworks/Python.framework/Versions/3.13/bin/python3 /Users/<USER>/Documents/augment-projects/heic_converter_uploader.py
```

## 📋 输出示例

### JPG/PNG文件：
```
🚀 开始处理文件: image.jpg
🔍 处理文件: image.jpg (格式: .jpg)
📸 JPG 格式，直接上传
📤 上传文件: image.jpg
✅ 上传成功
🎉 处理完成
```

### HEIC文件：
```
🚀 开始处理文件: photo.heic
🔍 处理文件: photo.heic (格式: .heic)
🔄 HEIC 格式，需要转换为PNG
🔄 转换 photo.heic -> photo.png
📐 已自动校正图片方向
✅ 转换完成: /path/to/photo.png
📤 上传文件: photo.png
✅ 上传成功
🎉 处理完成
```

## ⚠️ 注意事项

1. **确保PicGo已正确配置**
2. **HEIC转换需要pillow-heif库**
3. **转换后的PNG文件会保存在源文件夹**
4. **脚本会自动修正HEIC图片的方向问题**

## 🔧 依赖安装

如果需要安装依赖：
```bash
pip3 install pillow pillow-heif
```
