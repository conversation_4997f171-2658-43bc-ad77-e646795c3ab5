#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEIC转PNG并上传图床的脚本
用于Keyboard Maestro工作流
"""

import os
import sys
import subprocess
from pathlib import Path
from PIL import Image, ImageOps
import pillow_heif

def setup_heif_support():
    """设置HEIF格式支持"""
    try:
        pillow_heif.register_heif_opener()
        return True
    except Exception as e:
        print(f"❌ 设置HEIF支持失败: {e}")
        return False

def convert_heic_to_png(heic_path):
    """
    将HEIC文件转换为PNG格式，保存在同一目录
    返回PNG文件路径
    """
    try:
        heic_path = Path(heic_path)
        if not heic_path.exists():
            print(f"❌ 文件不存在: {heic_path}")
            return None
        
        # 生成PNG文件路径（同一目录，同名但扩展名为.png）
        png_path = heic_path.with_suffix('.png')
        
        print(f"🔄 转换 {heic_path.name} -> {png_path.name}")
        
        # 使用PIL打开并转换图片
        with Image.open(heic_path) as img:
            # 自动处理EXIF方向信息
            img = ImageOps.exif_transpose(img)
            print(f"📐 已自动校正图片方向")
            
            # 转换为RGB模式（PNG不支持某些颜色模式）
            if img.mode in ('RGBA', 'LA'):
                # 保持透明度
                png_img = img
            else:
                # 转换为RGB
                png_img = img.convert('RGB')
            
            # 保存为PNG
            png_img.save(png_path, 'PNG', optimize=True)
            print(f"✅ 转换完成: {png_path}")
            
        return str(png_path)
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return None

def upload_to_picgo(file_path):
    """使用PicGo上传文件"""
    try:
        print(f"📤 上传文件: {Path(file_path).name}")
        
        # 执行PicGo上传命令
        result = subprocess.run([
            '/Applications/PicGo.app/Contents/MacOS/PicGo',
            'upload',
            file_path
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"✅ 上传成功")
            if result.stdout.strip():
                print(f"📋 输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ 上传失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 上传超时")
        return False
    except Exception as e:
        print(f"❌ 上传出错: {e}")
        return False

def process_file(file_path):
    """
    处理单个文件：检查格式，必要时转换，然后上传
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 获取文件扩展名
    ext = file_path.suffix.lower()
    
    print(f"🔍 处理文件: {file_path.name} (格式: {ext})")
    
    # 根据文件格式决定处理方式
    if ext in ['.jpg', '.jpeg', '.png']:
        # JPG/PNG格式直接上传
        print(f"📸 {ext.upper()} 格式，直接上传")
        upload_path = str(file_path)
    elif ext in ['.heic', '.heif']:
        # HEIC格式需要先转换
        print(f"🔄 HEIC 格式，需要转换为PNG")
        upload_path = convert_heic_to_png(file_path)
        if not upload_path:
            return False
    else:
        print(f"⚠️  不支持的格式: {ext}")
        return False
    
    # 上传文件
    return upload_to_picgo(upload_path)

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("❌ 用法: python3 heic_converter_uploader.py <文件路径>")
        return False
    
    file_path = sys.argv[1]
    
    print(f"🚀 开始处理文件: {Path(file_path).name}")
    
    # 设置HEIF支持
    if not setup_heif_support():
        print("❌ 无法设置HEIF支持，HEIC转换将失败")
        # 继续执行，可能文件不是HEIC格式
    
    # 处理文件
    success = process_file(file_path)
    
    if success:
        print("🎉 处理完成")
        return True
    else:
        print("❌ 处理失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
