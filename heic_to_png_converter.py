#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEIC转PNG转换器
用于Keyboard Maestro工作流
只转换HEIC文件为PNG，保存在同一文件夹
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageOps
import pillow_heif

def setup_heif_support():
    """设置HEIF格式支持"""
    try:
        pillow_heif.register_heif_opener()
        return True
    except Exception as e:
        print(f"❌ 设置HEIF支持失败: {e}")
        return False

def convert_heic_to_png(heic_path):
    """
    将HEIC文件转换为PNG格式，保存在同一目录
    返回PNG文件路径
    """
    try:
        heic_path = Path(heic_path)
        if not heic_path.exists():
            print(f"❌ 文件不存在: {heic_path}")
            return None
        
        # 生成PNG文件路径（同一目录，同名但扩展名为.png）
        png_path = heic_path.with_suffix('.png')
        
        # 检查PNG文件是否已存在
        if png_path.exists():
            print(f"⚠️  PNG文件已存在: {png_path.name}")
            # 生成带时间戳的文件名避免覆盖
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            png_path = heic_path.with_name(f"{heic_path.stem}_{timestamp}.png")
            print(f"🔄 使用新文件名: {png_path.name}")
        
        print(f"🔄 转换 {heic_path.name} -> {png_path.name}")
        
        # 使用PIL打开并转换图片
        with Image.open(heic_path) as img:
            # 自动处理EXIF方向信息，修正图片方向
            img = ImageOps.exif_transpose(img)
            print(f"📐 已自动校正图片方向")
            
            # 转换为合适的模式
            if img.mode in ('RGBA', 'LA'):
                # 保持透明度
                png_img = img
            else:
                # 转换为RGB
                png_img = img.convert('RGB')
            
            # 保存为PNG
            png_img.save(png_path, 'PNG', optimize=True)
            print(f"✅ 转换完成: {png_path}")
            
        return str(png_path)
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def process_file(file_path):
    """
    处理单个文件：检查是否为HEIC格式，如果是则转换为PNG
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 获取文件扩展名
    ext = file_path.suffix.lower()
    
    print(f"🔍 处理文件: {file_path.name} (格式: {ext})")
    
    # 检查是否为HEIC格式
    if ext in ['.heic', '.heif']:
        print(f"🔄 检测到HEIC格式，开始转换...")
        result = convert_heic_to_png(file_path)
        return result is not None
    else:
        print(f"ℹ️  非HEIC格式 ({ext})，跳过转换")
        return True  # 非HEIC文件视为成功（不需要处理）

def main():
    """主函数"""
    # 从环境变量获取文件路径（Keyboard Maestro传递）
    file_path = os.environ.get('KMVAR_ImagePath')
    
    if not file_path:
        # 如果没有环境变量，尝试从命令行参数获取
        if len(sys.argv) >= 2:
            file_path = sys.argv[1]
        else:
            print("❌ 未找到文件路径")
            print("💡 请确保在Keyboard Maestro中设置了ImagePath变量")
            return False
    
    print(f"🚀 HEIC转PNG转换器启动")
    print(f"📁 目标文件: {Path(file_path).name}")
    
    # 设置HEIF支持
    if not setup_heif_support():
        print("❌ 无法设置HEIF支持，转换将失败")
        return False
    
    # 处理文件
    success = process_file(file_path)
    
    if success:
        print("🎉 处理完成")
        return True
    else:
        print("❌ 处理失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
