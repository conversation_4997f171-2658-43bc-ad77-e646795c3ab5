# HEIC转PNG脚本

## 功能说明
这个Python脚本可以将Mac备忘录中选中的HEIC照片转换为PNG格式并复制到剪切板。

## 主要特性
- 🖼️ **自动获取**: 从备忘录中自动获取选中的图片
- 🔄 **格式转换**: 支持HEIC转PNG格式转换
- 📋 **剪切板操作**: 转换后自动复制到剪切板
- ⚡ **快捷触发**: 通过Keyboard Maestro快捷键触发
- 🎯 **智能检测**: 自动检测图片格式，PNG格式无需转换

## 系统要求
- macOS系统
- Python 3.7+
- 备忘录应用

## 安装依赖
首次使用前需要安装依赖：
```bash
./install_dependencies.sh
```

或手动安装：
```bash
pip3 install Pillow pillow-heif
```

## 使用方法

### 1. 手动运行
1. 打开备忘录应用
2. 选中一张HEIC图片
3. 运行脚本：
   ```bash
   python3 heic_to_png.py
   ```
   或
   ```bash
   ./heic_to_png.py
   ```

### 2. 通过Keyboard Maestro触发（推荐）

#### 设置步骤：
1. 打开Keyboard Maestro
2. 创建新的宏(Macro)
3. 设置触发条件：
   - 快捷键（如：⌘⇧P）
   - 或应用特定触发（仅在备忘录中生效）
4. 添加动作：Execute a Shell Script
5. 脚本内容：
   ```bash
   cd /Users/<USER>/Documents/augment-projects/rename
   python3 heic_to_png.py
   ```

#### 使用流程：
1. 在备忘录中选中HEIC图片
2. 按设定的快捷键
3. 等待转换完成
4. PNG图片已复制到剪切板，可直接粘贴使用

## 支持的图片格式

### 输入格式
- HEIC/HEIF (主要目标)
- PNG (直接复制，无需转换)
- 其他PIL支持的格式

### 输出格式
- PNG (高质量，支持透明度)

## 脚本输出说明

### 成功示例
```
🔄 开始HEIC转PNG处理...
📝 从备忘录复制选中的图片...
📋 从剪切板获取图片数据...
✅ 获取到 HEIC 格式图片，大小: 2048576 字节
🔄 转换HEIC为PNG...
✅ 转换成功，PNG大小: 1536789 字节
📋 复制PNG到剪切板...
✅ 成功！PNG图片已复制到剪切板
```

### 错误处理
- 如果没有选中图片：提示选中图片
- 如果格式不支持：显示错误信息
- 如果转换失败：显示具体错误原因

## 注意事项
- 确保在备忘录中**选中**了图片，而不是仅仅点击
- 脚本会自动激活备忘录应用并执行复制操作
- 转换过程中请不要操作剪切板
- 大图片转换可能需要几秒钟时间

## 故障排除

### 1. 依赖安装失败
```bash
# 更新pip
pip3 install --upgrade pip

# 重新安装依赖
pip3 install --force-reinstall Pillow pillow-heif
```

### 2. 权限问题
```bash
# 给脚本添加执行权限
chmod +x heic_to_png.py
chmod +x install_dependencies.sh
```

### 3. 无法获取图片
- 确保在备忘录中选中了图片
- 尝试先手动复制图片(⌘C)再运行脚本
- 检查图片格式是否支持

## 文件说明
- `heic_to_png.py` - 主脚本文件
- `install_dependencies.sh` - 依赖安装脚本
- `HEIC_TO_PNG_README.md` - 本说明文档
