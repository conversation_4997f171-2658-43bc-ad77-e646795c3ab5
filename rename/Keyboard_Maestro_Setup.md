# Keyboard Maestro 设置说明

## 🎯 问题解决方案

您的HEIC转PNG脚本已经修复了旋转问题，现在需要正确配置Keyboard Maestro来使用它。

## 📋 修复内容

✅ **已修复图片旋转问题**
- 使用 `ImageOps.exif_transpose()` 自动处理EXIF方向
- 支持所有标准EXIF方向值
- 特别修复了方向值6（最常见的手机拍照方向）的处理

## 🔧 Keyboard Maestro 配置

### 方案1：使用包装器脚本（推荐）

1. **创建新的Macro**
2. **设置触发器**（如快捷键）
3. **添加"Execute a Shell Script"动作**
4. **脚本内容：**
   ```bash
   /Library/Frameworks/Python.framework/Versions/3.13/bin/python3 /Users/<USER>/Documents/augment-projects/rename/km_heic_to_png.py
   ```

### 方案2：直接调用主脚本

1. **创建新的Macro**
2. **设置触发器**（如快捷键）
3. **添加"Execute a Shell Script"动作**
4. **脚本内容：**
   ```bash
   /Library/Frameworks/Python.framework/Versions/3.13/bin/python3 /Users/<USER>/Documents/augment-projects/rename/heic_to_png.py
   ```

## 📝 正确使用流程

### 重要：必须按以下顺序操作

1. **打开备忘录应用**
2. **找到包含HEIC图片的备忘录**
3. **点击选中图片**（图片会有蓝色边框）
4. **按 ⌘C 复制图片**
5. **立即触发Keyboard Maestro宏**

### ⚠️ 常见错误

- ❌ 选中了文字而不是图片
- ❌ 复制了包含图片的文字段落
- ❌ 复制图片后等待太久才运行脚本

## 🔍 调试方法

### 查看日志文件

脚本会在桌面创建日志文件：
- `heic_to_png_log.txt` - 主脚本日志
- `km_heic_log.txt` - Keyboard Maestro包装器日志

### 测试脚本

可以在终端中直接运行测试：
```bash
cd "/Users/<USER>/Documents/augment-projects/rename"
python3 heic_to_png_simple.py  # 简单测试
python3 heic_to_png.py         # 完整测试
```

## 🎉 预期结果

成功执行后：
1. 图片会转换为PNG格式
2. **方向会自动校正**（不再向左旋转90度）
3. PNG图片会复制到剪切板
4. 可以直接粘贴到其他应用

## 🆘 故障排除

### 如果仍然出现旋转问题

1. 检查日志文件中的EXIF方向值
2. 确认使用的是修复后的脚本版本
3. 尝试用不同的HEIC图片测试

### 如果脚本无法运行

1. 检查Python路径：`which python3`
2. 检查依赖包：`python3 -c "import PIL, pillow_heif"`
3. 查看桌面上的日志文件

### 如果无法获取剪切板图片

1. 确保选中的是图片本身，不是文字
2. 复制后立即运行脚本
3. 检查备忘录中的图片格式

## 📞 技术支持

如果问题仍然存在，请提供：
1. 桌面上的日志文件内容
2. 使用的macOS版本
3. 备忘录中图片的具体格式
