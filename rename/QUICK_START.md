# HEIC转PNG 快速使用指南

## 🚀 快速测试

### 1. 复制图片到剪切板
- 打开**备忘录**或**照片**应用
- 选中一张图片（任意格式）
- 按 **⌘C** 复制

### 2. 运行脚本
```bash
cd /Users/<USER>/Documents/augment-projects/rename
python3 heic_to_png.py
```

## 🔧 Keyboard Maestro 设置

### 创建宏
1. 打开 **Keyboard Maestro**
2. 点击 **New Macro**
3. 设置名称：`HEIC转PNG`
4. 设置触发器：**Hot Key Trigger** (比如 ⌘⇧P)

### 添加动作
1. 点击 **New Action**
2. 选择 **Execute a Shell Script**
3. 脚本内容：
```bash
cd /Users/<USER>/Documents/augment-projects/rename
python3 heic_to_png.py
```
4. 设置 **Display results in a window** (可选，用于查看输出)

## 📱 使用流程
1. 在备忘录中选中HEIC图片
2. 按 ⌘C 复制
3. 按你设置的快捷键（如 ⌘⇧P）
4. 等待转换完成
5. PNG图片已在剪切板，可直接粘贴

## ✅ 修复内容
- **方向校正**: 自动处理EXIF方向信息，防止图片旋转
- **错误处理**: 改进Keyboard Maestro兼容性
- **调试信息**: 显示详细的处理过程

## 🐛 故障排除
- 如果提示"无法获取图片数据"：确保先复制了图片
- 如果Keyboard Maestro报错：检查脚本路径是否正确
- 如果图片方向不对：脚本已自动修复EXIF方向问题
