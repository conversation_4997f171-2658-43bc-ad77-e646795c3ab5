# 音乐文件统计脚本

## 功能说明
这个Python脚本用于统计 `/Volumes/music/虾米网易云` 路径下的音乐文件数量，包括：
- 各文件夹的音乐文件数量
- 各种音乐格式的文件数量统计
- 总文件数量

## 支持的音乐格式
- MP3, FLAC, WAV, AAC, M4A
- OGG, WMA, APE, DSD, DSF, DFF
- OPUS, AIFF, ALAC

## 使用方法

### 1. 直接运行
```bash
python3 music_stats.py
```

### 2. 通过 Keyboard Maestro 触发
1. 打开 Keyboard Maestro
2. 创建新的宏 (Macro)
3. 设置触发条件（快捷键、定时等）
4. 添加动作：Execute a Shell Script
5. 在脚本内容中输入：
   ```bash
   cd /Users/<USER>/Documents/augment-projects/rename
   python3 music_stats.py
   ```

## 输出文件
脚本会在同目录下生成 `music_stats.txt` 文件，包含详细的统计报告。

## 历史记录功能
- **保留历史统计**: 每次运行都会将新的统计结果追加到文件末尾
- **不覆盖旧数据**: 之前的统计记录会被完整保留
- **时间戳记录**: 每次统计都有独立的时间戳，便于追踪变化

## 注意事项
- 确保目标路径 `/Volumes/music/虾米网易云` 存在且可访问
- 脚本会递归扫描所有子文件夹
- 每次运行都会**追加**新的统计结果，保留历史记录
- 如果路径不存在，会在输出文件中记录错误信息
