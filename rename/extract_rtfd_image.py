#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从RTFD格式中提取图片数据
"""

import subprocess
import tempfile
import os
import zipfile
from pathlib import Path

def extract_image_from_rtfd():
    """从剪切板的RTFD数据中提取图片"""
    print("🔍 尝试从RTFD格式提取图片...")
    
    try:
        # 获取RTFD数据
        script = '''
        try
            set rtfdData to the clipboard as «class rtfd»
            return rtfdData
        on error errMsg
            return "error: " & errMsg
        end try
        '''
        
        result = subprocess.run(['osascript', '-e', script],
                              capture_output=True, text=False)
        
        if result.returncode != 0:
            print("❌ 无法获取RTFD数据")
            return None
            
        rtfd_data = result.stdout
        print(f"✅ 获取到RTFD数据，大小: {len(rtfd_data)} 字节")
        
        # 保存RTFD数据到临时文件
        with tempfile.NamedTemporaryFile(suffix='.rtfd', delete=False) as temp_file:
            temp_file.write(rtfd_data)
            rtfd_path = temp_file.name
        
        print(f"📁 RTFD临时文件: {rtfd_path}")
        
        # RTFD实际上是一个包含多个文件的目录结构
        # 尝试解析其中的图片文件
        try:
            # 方法1: 直接读取为目录（如果系统支持）
            rtfd_dir = rtfd_path
            if os.path.isfile(rtfd_path):
                # 如果是文件，尝试重命名为目录
                rtfd_dir = rtfd_path + "_dir"
                os.rename(rtfd_path, rtfd_dir)
            
            # 查找图片文件
            image_files = []
            if os.path.exists(rtfd_dir):
                for root, dirs, files in os.walk(rtfd_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # 检查文件扩展名
                        ext = Path(file).suffix.lower()
                        if ext in ['.heic', '.jpg', '.jpeg', '.png', '.tiff', '.gif']:
                            image_files.append(file_path)
                            print(f"🖼️ 找到图片文件: {file}")
                        # 也检查没有扩展名但可能是图片的文件
                        elif not ext and os.path.getsize(file_path) > 1000:
                            # 检查文件头
                            with open(file_path, 'rb') as f:
                                header = f.read(20)
                                if (header.startswith(b'\x89PNG') or 
                                    header.startswith(b'\xff\xd8\xff') or
                                    header.startswith(b'RIFF') or
                                    b'ftyp' in header):  # HEIC/MOV格式
                                    image_files.append(file_path)
                                    print(f"🖼️ 找到图片文件（无扩展名）: {file}")
            
            if image_files:
                # 返回第一个找到的图片文件
                image_path = image_files[0]
                with open(image_path, 'rb') as f:
                    image_data = f.read()
                
                # 尝试确定格式
                format_name = "unknown"
                if image_data.startswith(b'\x89PNG'):
                    format_name = "png"
                elif image_data.startswith(b'\xff\xd8\xff'):
                    format_name = "jpg"
                elif b'ftyp' in image_data[:20]:
                    format_name = "heic"
                
                print(f"✅ 提取到图片，格式: {format_name}, 大小: {len(image_data)} 字节")
                
                # 清理临时文件
                try:
                    if os.path.exists(rtfd_dir):
                        import shutil
                        shutil.rmtree(rtfd_dir)
                except:
                    pass
                
                return image_data, format_name
            else:
                print("❌ 在RTFD中未找到图片文件")
                
        except Exception as e:
            print(f"❌ 解析RTFD时出错: {e}")
        
        # 清理临时文件
        try:
            if os.path.exists(rtfd_path):
                os.unlink(rtfd_path)
            if os.path.exists(rtfd_dir):
                import shutil
                shutil.rmtree(rtfd_dir)
        except:
            pass
            
        return None, None
        
    except Exception as e:
        print(f"❌ 提取RTFD图片时出错: {e}")
        return None, None

if __name__ == "__main__":
    image_data, format_name = extract_image_from_rtfd()
    if image_data:
        print(f"🎉 成功提取图片: {format_name}, {len(image_data)} 字节")
    else:
        print("❌ 提取失败")
