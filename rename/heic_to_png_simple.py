#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版HEIC转PNG脚本 - 用于调试
"""

import os
import sys
import subprocess
from datetime import datetime

def log_message(message):
    """记录日志消息"""
    log_file = os.path.expanduser("~/Desktop/heic_debug.txt")
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}\n"
    
    print(message)
    try:
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(log_entry)
    except Exception as e:
        print(f"日志写入失败: {e}")

def check_environment():
    """检查运行环境"""
    log_message("🔍 检查运行环境...")
    log_message(f"🐍 Python版本: {sys.version}")
    log_message(f"📁 当前目录: {os.getcwd()}")
    log_message(f"📄 脚本路径: {os.path.abspath(__file__)}")
    log_message(f"🔧 Python可执行文件: {sys.executable}")
    
    # 检查PIL
    try:
        from PIL import Image, ImageOps
        log_message(f"✅ PIL导入成功")
    except ImportError as e:
        log_message(f"❌ PIL导入失败: {e}")
        return False
    
    # 检查pillow_heif
    try:
        import pillow_heif
        log_message(f"✅ pillow_heif导入成功")
    except ImportError as e:
        log_message(f"❌ pillow_heif导入失败: {e}")
        return False
    
    return True

def check_clipboard():
    """检查剪切板内容"""
    log_message("📋 检查剪切板内容...")
    try:
        result = subprocess.run(['osascript', '-e', 'clipboard info'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            clipboard_info = result.stdout.strip()
            log_message(f"📋 剪切板内容: {clipboard_info}")
            return clipboard_info
        else:
            log_message(f"❌ 获取剪切板信息失败: {result.stderr}")
            return None
    except subprocess.TimeoutExpired:
        log_message("❌ 获取剪切板信息超时")
        return None
    except Exception as e:
        log_message(f"❌ 检查剪切板时出错: {e}")
        return None

def main():
    """主函数"""
    log_file = os.path.expanduser("~/Desktop/heic_debug.txt")
    
    # 清空日志文件
    try:
        with open(log_file, "w", encoding="utf-8") as f:
            f.write("=== HEIC转PNG调试日志 ===\n")
    except:
        pass
    
    log_message("🚀 开始调试...")
    
    # 检查环境
    if not check_environment():
        log_message("❌ 环境检查失败")
        return False
    
    # 检查剪切板
    clipboard_info = check_clipboard()
    if clipboard_info is None:
        log_message("❌ 无法获取剪切板信息")
        return False
    
    log_message("✅ 基础检查完成")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("✅ 调试脚本执行成功")
        else:
            print("❌ 调试脚本执行失败")
    except Exception as e:
        print(f"❌ 调试脚本出错: {e}")
        import traceback
        traceback.print_exc()
