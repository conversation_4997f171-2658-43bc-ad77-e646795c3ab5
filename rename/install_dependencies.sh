#!/bin/bash
# 安装HEIC转PNG脚本所需的依赖

echo "🔧 安装HEIC转PNG脚本依赖..."

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

echo "✅ Python3 已安装: $(python3 --version)"

# 检查pip是否安装
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi

echo "✅ pip3 已安装"

# 安装必需的Python包
echo "📦 安装Python依赖包..."

# 安装Pillow (PIL)
echo "安装 Pillow..."
pip3 install Pillow

# 安装pillow-heif (HEIF支持)
echo "安装 pillow-heif..."
pip3 install pillow-heif

echo "✅ 依赖安装完成！"
echo ""
echo "现在可以运行 heic_to_png.py 脚本了"
echo "使用方法："
echo "1. 在备忘录中选中HEIC图片"
echo "2. 运行脚本: python3 heic_to_png.py"
echo "3. 或通过 Keyboard Maestro 触发"
