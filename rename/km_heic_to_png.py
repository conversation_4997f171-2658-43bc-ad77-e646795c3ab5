#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keyboard Maestro专用的HEIC转PNG脚本包装器
解决Keyboard Maestro环境下的执行问题
"""

import os
import sys
import subprocess
from datetime import datetime

def setup_environment():
    """设置执行环境"""
    # 确保使用正确的Python路径
    python_paths = [
        '/usr/local/bin/python3',
        '/opt/homebrew/bin/python3',
        '/usr/bin/python3',
        sys.executable
    ]
    
    for python_path in python_paths:
        if os.path.exists(python_path):
            return python_path
    
    return sys.executable

def log_to_desktop(message):
    """记录日志到桌面"""
    log_file = os.path.expanduser("~/Desktop/km_heic_log.txt")
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    try:
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"[{timestamp}] {message}\n")
    except:
        pass
    
    print(message)

def main():
    """主函数"""
    log_file = os.path.expanduser("~/Desktop/km_heic_log.txt")
    
    # 初始化日志
    try:
        with open(log_file, "w", encoding="utf-8") as f:
            f.write("=== Keyboard Maestro HEIC转PNG日志 ===\n")
    except:
        pass
    
    log_to_desktop("🚀 Keyboard Maestro包装器启动")
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    main_script = os.path.join(script_dir, "heic_to_png.py")
    
    log_to_desktop(f"📁 脚本目录: {script_dir}")
    log_to_desktop(f"📄 主脚本路径: {main_script}")
    
    if not os.path.exists(main_script):
        log_to_desktop(f"❌ 主脚本不存在: {main_script}")
        return False
    
    # 设置环境
    python_executable = setup_environment()
    log_to_desktop(f"🐍 使用Python: {python_executable}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = script_dir
    env['PATH'] = '/usr/local/bin:/opt/homebrew/bin:' + env.get('PATH', '')
    
    try:
        log_to_desktop("🔄 执行主脚本...")
        
        # 执行主脚本
        result = subprocess.run(
            [python_executable, main_script],
            capture_output=True,
            text=True,
            timeout=120,  # 2分钟超时
            env=env,
            cwd=script_dir
        )
        
        log_to_desktop(f"📤 返回码: {result.returncode}")
        
        if result.stdout:
            log_to_desktop("📝 标准输出:")
            for line in result.stdout.strip().split('\n'):
                log_to_desktop(f"  {line}")
        
        if result.stderr:
            log_to_desktop("⚠️ 错误输出:")
            for line in result.stderr.strip().split('\n'):
                log_to_desktop(f"  {line}")
        
        if result.returncode == 0:
            log_to_desktop("✅ 脚本执行成功")
            return True
        else:
            log_to_desktop(f"❌ 脚本执行失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        log_to_desktop("❌ 脚本执行超时")
        return False
    except Exception as e:
        log_to_desktop(f"❌ 执行脚本时出错: {e}")
        import traceback
        log_to_desktop(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("✅ Keyboard Maestro包装器执行成功")
            sys.exit(0)
        else:
            print("❌ Keyboard Maestro包装器执行失败")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 包装器出错: {e}")
        sys.exit(1)
