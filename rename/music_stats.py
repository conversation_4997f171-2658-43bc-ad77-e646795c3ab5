#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐文件统计脚本
通过 Keyboard Maestro 触发，统计指定路径下的音乐文件数量
"""

import os
import datetime
from collections import defaultdict, Counter
from pathlib import Path

# 音乐文件扩展名
MUSIC_EXTENSIONS = {
    '.mp3', '.flac', '.wav', '.aac', '.m4a', '.ogg', '.wma', 
    '.ape', '.dsd', '.dsf', '.dff', '.opus', '.aiff', '.alac'
}

def get_music_files_stats(root_path):
    """
    统计指定路径下的音乐文件
    返回：文件夹统计、格式统计、总数
    """
    root_path = Path(root_path)
    
    if not root_path.exists():
        return None, None, 0, f"路径不存在: {root_path}"
    
    folder_stats = defaultdict(int)  # 每个文件夹的音乐文件数量
    format_stats = Counter()         # 各种格式的文件数量
    total_count = 0
    
    try:
        # 遍历所有子文件夹
        for item in root_path.iterdir():
            if item.is_dir():
                folder_name = item.name
                folder_music_count = 0
                
                # 递归遍历文件夹中的所有文件
                for file_path in item.rglob('*'):
                    if file_path.is_file():
                        file_ext = file_path.suffix.lower()
                        if file_ext in MUSIC_EXTENSIONS:
                            folder_music_count += 1
                            format_stats[file_ext] += 1
                            total_count += 1
                
                folder_stats[folder_name] = folder_music_count
        
        return dict(folder_stats), dict(format_stats), total_count, None
    
    except Exception as e:
        return None, None, 0, f"统计过程中出错: {str(e)}"

def generate_report(folder_stats, format_stats, total_count):
    """
    生成统计报告文本
    """
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report_lines = [
        "=" * 60,
        f"音乐文件统计报告",
        f"统计时间: {current_time}",
        f"统计路径: /Volumes/music/虾米网易云",
        "=" * 60,
        "",
        "📁 各文件夹音乐文件数量:",
        "-" * 40
    ]
    
    # 按文件夹名称排序
    if folder_stats:
        for folder_name in sorted(folder_stats.keys()):
            count = folder_stats[folder_name]
            report_lines.append(f"  {folder_name}: {count} 个文件")
    else:
        report_lines.append("  (无音乐文件)")
    
    report_lines.extend([
        "",
        "🎵 各格式文件统计:",
        "-" * 40
    ])
    
    # 按格式名称排序
    if format_stats:
        for ext in sorted(format_stats.keys()):
            count = format_stats[ext]
            report_lines.append(f"  {ext.upper()}: {count} 个文件")
    else:
        report_lines.append("  (无音乐文件)")
    
    report_lines.extend([
        "",
        "📊 总计:",
        "-" * 40,
        f"  总音乐文件数: {total_count} 个",
        f"  文件夹数量: {len(folder_stats) if folder_stats else 0} 个",
        f"  音乐格式种类: {len(format_stats) if format_stats else 0} 种",
        "",
        "=" * 60
    ])
    
    return "\n".join(report_lines)

def main():
    """
    主函数
    """
    # 目标路径
    music_path = "/Volumes/music/虾米网易云"
    
    # 输出文件路径（在脚本同目录下）
    script_dir = Path(__file__).parent
    output_file = script_dir / "music_stats.txt"
    
    print(f"开始统计音乐文件...")
    print(f"目标路径: {music_path}")
    
    # 获取统计数据
    folder_stats, format_stats, total_count, error = get_music_files_stats(music_path)
    
    if error:
        error_msg = f"统计失败: {error}"
        print(error_msg)

        # 追加错误信息到文件
        file_exists = output_file.exists()
        with open(output_file, 'a', encoding='utf-8') as f:
            if file_exists:
                f.write("\n\n")
            f.write("=" * 60 + "\n")
            f.write(f"音乐文件统计报告\n")
            f.write(f"统计时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"错误信息: {error}\n")
            f.write("=" * 60 + "\n")
        return
    
    # 生成报告
    report = generate_report(folder_stats, format_stats, total_count)
    
    # 追加写入文件（保留历史记录）
    try:
        # 检查文件是否存在，如果存在则添加分隔符
        file_exists = output_file.exists()

        with open(output_file, 'a', encoding='utf-8') as f:
            # 如果文件已存在，添加换行分隔符
            if file_exists:
                f.write("\n\n")
            f.write(report)

        print(f"统计完成!")
        print(f"总共找到 {total_count} 个音乐文件")
        print(f"报告已追加到: {output_file}")

        # 显示简要统计
        if folder_stats:
            print(f"\n文件夹数量: {len(folder_stats)}")
            print(f"音乐格式: {len(format_stats)} 种")
            top_folders = sorted(folder_stats.items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"文件最多的前5个文件夹:")
            for folder, count in top_folders:
                print(f"  {folder}: {count} 个文件")

    except Exception as e:
        print(f"写入文件失败: {str(e)}")

if __name__ == "__main__":
    main()
