#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的剪切板获取方法
"""

import subprocess
import os

def test_clipboard_methods():
    """测试多种剪切板获取方法"""
    print("🔍 测试剪切板内容获取方法...")
    
    # 方法1: osascript clipboard info
    print("\n📋 方法1: osascript clipboard info")
    try:
        result = subprocess.run(['osascript', '-e', 'clipboard info'], 
                              capture_output=True, text=True)
        print(f"结果: {result.stdout.strip()}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 方法2: 尝试获取图片数据
    print("\n📋 方法2: 尝试获取图片数据")
    formats_to_try = [
        ('«class PNGf»', 'PNG'),
        ('«class HEIC»', 'HEIC'), 
        ('«class JPEG»', 'JPEG'),
        ('«class TIFF»', 'TIFF'),
        ('picture', 'Picture')
    ]
    
    for format_class, format_name in formats_to_try:
        try:
            script = f'''
            try
                set clipboardData to the clipboard as {format_class}
                return "success"
            on error errMsg
                return "error: " & errMsg
            end try
            '''
            result = subprocess.run(['osascript', '-e', script],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                output = result.stdout.strip()
                if output == "success":
                    print(f"✅ {format_name}: 成功")
                else:
                    print(f"❌ {format_name}: {output}")
            else:
                print(f"❌ {format_name}: 执行失败")
        except Exception as e:
            print(f"❌ {format_name}: {e}")
    
    # 方法3: pbpaste
    print("\n📋 方法3: pbpaste")
    try:
        result = subprocess.run(['pbpaste'], capture_output=True)
        if result.returncode == 0:
            data_size = len(result.stdout)
            print(f"数据大小: {data_size} 字节")
            if data_size > 1000:
                print("✅ 可能包含图片数据")
                # 检查文件头
                if result.stdout.startswith(b'\x89PNG'):
                    print("🖼️ 检测到PNG格式")
                elif result.stdout.startswith(b'\xff\xd8\xff'):
                    print("🖼️ 检测到JPEG格式")
                else:
                    print("🔍 未知格式，显示前20字节:")
                    print(result.stdout[:20])
            else:
                print("❌ 数据太小，可能不是图片")
                print(f"内容: {result.stdout.decode('utf-8', errors='ignore')[:100]}")
        else:
            print("❌ pbpaste失败")
    except Exception as e:
        print(f"❌ pbpaste错误: {e}")

if __name__ == "__main__":
    test_clipboard_methods()
