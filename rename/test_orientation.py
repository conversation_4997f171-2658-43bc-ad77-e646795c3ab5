#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EXIF方向处理的脚本
"""

import tempfile
from PIL import Image, ImageOps
import pillow_heif

def test_orientation_handling():
    """
    测试方向处理功能
    """
    print("🧪 测试EXIF方向处理功能...")
    
    # 设置HEIF支持
    try:
        pillow_heif.register_heif_opener()
        print("✅ HEIF支持已启用")
    except Exception as e:
        print(f"❌ HEIF支持启用失败: {e}")
        return False
    
    # 创建一个测试图片（带有文字，方便观察方向）
    try:
        # 创建一个简单的测试图片
        test_img = Image.new('RGB', (200, 100), color='white')
        
        # 在图片上添加文字来标识方向
        try:
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(test_img)
            # 使用默认字体
            draw.text((10, 10), "TOP", fill='black')
            draw.text((10, 80), "BOTTOM", fill='black')
            draw.text((10, 45), "LEFT", fill='black')
            draw.text((150, 45), "RIGHT", fill='black')
        except:
            print("⚠️  无法添加文字，但测试继续")
        
        # 保存测试图片
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            test_img.save(temp_file.name, 'PNG')
            test_path = temp_file.name
        
        print(f"✅ 创建测试图片: {test_path}")
        
        # 测试ImageOps.exif_transpose功能
        with Image.open(test_path) as img:
            print(f"📏 原始图片尺寸: {img.size}")
            
            # 应用exif_transpose
            corrected_img = ImageOps.exif_transpose(img)
            print(f"📏 校正后图片尺寸: {corrected_img.size}")
            
            if corrected_img.size == img.size:
                print("✅ ImageOps.exif_transpose 功能正常")
            else:
                print("⚠️  图片尺寸发生变化，可能进行了旋转")
        
        # 清理测试文件
        import os
        os.unlink(test_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_orientation_handling()
    if success:
        print("🎉 测试完成")
    else:
        print("❌ 测试失败")
